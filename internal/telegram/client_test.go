package telegram

import (
	"testing"
)

func TestEscapeMarkdownV2(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello_world", "hello\\_world"},
		{"test*bold*", "test\\*bold\\*"},
		{"link[text](url)", "link\\[text\\]\\(url\\)"},
		{"normal text", "normal text"},
		{"", ""},
	}

	for _, test := range tests {
		result := EscapeMarkdownV2(test.input)
		if result != test.expected {
			t.<PERSON>rro<PERSON>("EscapeMarkdownV2(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestFormatMessage(t *testing.T) {
	client := NewClient("test-key", 123)
	
	msg := Message{
		OfferName: "Test Offer",
		OfferDate: "2024-01-01",
		Website:   "https://example.com",
	}

	result := client.FormatMessage(msg)
	expected := "🎁 *Test Offer*\n📅 2024\\-01\\-01\n\n🔗 [Ver detalhes](https://example.com)"

	if result != expected {
		t.<PERSON><PERSON><PERSON>("FormatMessage() = %q, expected %q", result, expected)
	}
}