package telegram

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
)

type Client struct {
	api<PERSON>ey    string
	channelID int64
}

type Message struct {
	OfferName string
	OfferDate string
	Website   string
}

func NewClient(apiKey string, channelID int64) *Client {
	return &Client{
		apiKey:    apiKey,
		channelID: channelID,
	}
}

func (c *Client) SendMessage(msg Message) error {
	text := c.formatMessage(msg)
	
	apiURL := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", c.apiKey)
	
	payload := map[string]interface{}{
		"chat_id":    c.channelID,
		"text":       text,
		"parse_mode": "MarkdownV2",
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	resp, err := http.Post(apiURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("telegram API returned status %d: %s", resp.StatusCode, resp.Status)
	}

	return nil
}

func (c *Client) FormatMessage(msg Message) string {
	return fmt.Sprintf("🎁 *%s*\n📅 %s\n\n🔗 [Ver detalhes](%s)",
		EscapeMarkdownV2(msg.OfferName),
		EscapeMarkdownV2(msg.OfferDate),
		msg.Website,
	)
}

func (c *Client) formatMessage(msg Message) string {
	return c.FormatMessage(msg)
}

func EscapeMarkdownV2(input string) string {
	replacer := strings.NewReplacer(
		"_", `\_`,
		"*", `\*`,
		"[", `\[`,
		"]", `\]`,
		"(", `\(`,
		")", `\)`,
		"~", `\~`,
		"`", "\\`",
		">", `\>`,
		"#", `\#`,
		"-", `\-`,
		"+", `\+`,
		"=", `\=`,
		"|", `\|`,
		"{", `\{`,
		"}", `\}`,
		".", `\.`,
		"!", `\!`,
	)
	return replacer.Replace(input)
}