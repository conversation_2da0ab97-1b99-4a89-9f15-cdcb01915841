package storage

import (
	"os"
	"strings"
)

type Storage interface {
	GetLastMessage() (string, error)
	SaveLastMessage(message string) error
}

type FileStorage struct {
	filePath string
}

func NewFileStorage(filePath string) *FileStorage {
	return &FileStorage{filePath: filePath}
}

func (fs *FileStorage) GetLastMessage() (string, error) {
	data, err := os.ReadFile(fs.filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return "", nil
		}
		return "", err
	}
	return strings.TrimSpace(string(data)), nil
}

func (fs *FileStorage) SaveLastMessage(message string) error {
	return os.WriteFile(fs.filePath, []byte(message), 0644)
}