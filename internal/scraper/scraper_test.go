package scraper

import (
	"testing"
)

func TestCleanText(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"  hello  world  ", "helloworld"},
		{"hello\nworld", "hello world"},
		{"  multiple   spaces  ", "multiple spaces"},
		{"", ""},
		{"normal text", "normal text"},
	}

	for _, test := range tests {
		result := cleanText(test.input)
		if result != test.expected {
			t.<PERSON>("cleanText(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestRemoveExcessiveSpace(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello  world", "helloworld"},
		{"  hello    world  ", "helloworld"},
		{"no double spaces", "no double spaces"},
		{"", ""},
	}

	for _, test := range tests {
		result := removeExcessiveSpace(test.input)
		if result != test.expected {
			t.<PERSON>rrorf("removeExcessiveSpace(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}