package scraper

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/gocolly/colly"
)

type OfferData struct {
	Name    string
	Date    string
	Website string
}

type Scraper struct {
	collector *colly.Collector
}

func NewScraper() *Scraper {
	return &Scraper{
		collector: colly.NewCollector(),
	}
}

func (s *Scraper) ScrapeOffer(url string) (*OfferData, error) {
	offer := &OfferData{Website: url}
	var err error

	s.collector.OnHTML(".offer__name", func(e *colly.HTMLElement) {
		offer.Name = cleanText(e.DOM.AndSelf().Text())
	})

	s.collector.OnHTML(".bullet > li:nth-child(1)", func(e *colly.HTMLElement) {
		parsed := strings.TrimPrefix(cleanText(e.Text), " ")
		
		if strings.Contains(parsed, fmt.Sprintf("%d", time.Now().Year())) {
			offer.Date = parsed
		}
	})

	s.collector.OnError(func(r *colly.Response, e error) {
		err = fmt.Errorf("scraping error: %w", e)
	})

	if visitErr := s.collector.Visit(url); visitErr != nil {
		return nil, fmt.Errorf("failed to visit URL: %w", visitErr)
	}

	if err != nil {
		return nil, err
	}

	if offer.Name == "" || offer.Date == "" {
		return nil, fmt.Errorf("incomplete offer data: name='%s', date='%s'", offer.Name, offer.Date)
	}

	return offer, nil
}

func (s *Scraper) ScrapeMultipleOffers(urls []string) ([]*OfferData, error) {
	var offers []*OfferData
	var errors []string

	for _, url := range urls {
		offer, err := s.ScrapeOffer(url)
		if err != nil {
			errors = append(errors, fmt.Sprintf("failed to scrape %s: %v", url, err))
			continue
		}
		offers = append(offers, offer)
	}

	if len(offers) == 0 {
		return nil, fmt.Errorf("no offers found. Errors: %v", errors)
	}

	return offers, nil
}

func (s *Scraper) FindMostRecentOffer(urls []string) (*OfferData, error) {
	offers, err := s.ScrapeMultipleOffers(urls)
	if err != nil {
		return nil, err
	}

	if len(offers) == 0 {
		return nil, fmt.Errorf("no offers found")
	}

	mostRecent := offers[0]
	mostRecentDate, err := parseDate(mostRecent.Date)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date for offer %s: %w", mostRecent.Name, err)
	}

	for _, offer := range offers[1:] {
		offerDate, err := parseDate(offer.Date)
		if err != nil {
			continue // Skip offers with unparseable dates
		}

		if offerDate.After(mostRecentDate) {
			mostRecent = offer
			mostRecentDate = offerDate
		}
	}

	return mostRecent, nil
}

func parseDate(dateStr string) (time.Time, error) {
	// Extract date from text that might contain date ranges or additional text
	extractedDate := extractDateFromText(dateStr)
	if extractedDate == "" {
		return time.Time{}, fmt.Errorf("no date found in text: %s", dateStr)
	}
	
	// Try different date formats commonly used in Portuguese
	formats := []string{
		"2 de January de 2006",
		"02 de January de 2006", 
		"2006-01-02",
		"02/01/2006",
		"2/1/2006",
	}
	
	// Portuguese month names mapping
	months := map[string]string{
		"janeiro":   "January",
		"fevereiro": "February", 
		"março":     "March",
		"abril":     "April",
		"maio":      "May",
		"junho":     "June",
		"julho":     "July",
		"agosto":    "August",
		"setembro":  "September",
		"outubro":   "October",
		"novembro":  "November",
		"dezembro":  "December",
	}
	
	// Replace Portuguese month names with English ones
	englishDate := extractedDate
	for pt, en := range months {
		englishDate = strings.ReplaceAll(strings.ToLower(englishDate), pt, en)
	}
	
	for _, format := range formats {
		if t, err := time.Parse(format, englishDate); err == nil {
			return t, nil
		}
	}
	
	return time.Time{}, fmt.Errorf("unable to parse date: %s (extracted: %s)", dateStr, extractedDate)
}

func extractDateFromText(text string) string {
	// Handle date ranges like "19 a 21 de agosto de 2025" - use the end date
	// Pattern: number a number de month de year
	rangePattern := `(\d{1,2})\s+a\s+(\d{1,2})\s+de\s+(\w+)\s+de\s+(\d{4})`
	if matches := regexp.MustCompile(rangePattern).FindStringSubmatch(text); matches != nil {
		return fmt.Sprintf("%s de %s de %s", matches[2], matches[3], matches[4])
	}
	
	// Handle single dates like "21 de agosto de 2025"
	singlePattern := `(\d{1,2})\s+de\s+(\w+)\s+de\s+(\d{4})`
	if matches := regexp.MustCompile(singlePattern).FindStringSubmatch(text); matches != nil {
		return fmt.Sprintf("%s de %s de %s", matches[1], matches[2], matches[3])
	}
	
	// If no specific pattern matches, return the original text
	return text
}

func cleanText(text string) string {
	cleaned := strings.TrimSpace(text)
	cleaned = removeExcessiveSpace(cleaned)
	cleaned = strings.ReplaceAll(cleaned, "\n", " ")
	cleaned = strings.ReplaceAll(cleaned, "  ", " ")
	return cleaned
}

func removeExcessiveSpace(str string) string {
	newStr := strings.ReplaceAll(str, "  ", "")
	
	if len(newStr) == len(str) {
		return newStr
	}
	
	return removeExcessiveSpace(newStr)
}