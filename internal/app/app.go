package app

import (
	"fmt"
	"log"

	"github.com/edualm/wizink-current-promo/internal/config"
	"github.com/edualm/wizink-current-promo/internal/scraper"
	"github.com/edualm/wizink-current-promo/internal/storage"
	"github.com/edualm/wizink-current-promo/internal/telegram"
)

type App struct {
	config        *config.Config
	scraper       *scraper.Scraper
	telegramClient *telegram.Client
	storage       storage.Storage
}

func New() (*App, error) {
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}
	
	return &App{
		config:        cfg,
		scraper:       scraper.NewScraper(),
		telegramClient: telegram.NewClient(cfg.TelegramAPIKey, cfg.TelegramChannelID),
		storage:       storage.NewFileStorage(cfg.LastMessageFile),
	}, nil
}

func (a *App) Run() error {
	log.Println("Starting Wizink offer scraper...")

	offer, err := a.scraper.FindMostRecentOffer(a.config.Websites)
	if err != nil {
		return fmt.Errorf("failed to find most recent offer: %w", err)
	}

	log.Printf("Found most recent offer: %s - %s (from %s)", offer.Name, offer.Date, offer.Website)

	message := telegram.Message{
		OfferName: offer.Name,
		OfferDate: offer.Date,
		Website:   offer.Website,
	}

	// Check for duplicate message
	lastMessage, err := a.storage.GetLastMessage()
	if err != nil {
		log.Printf("Warning: failed to read last message: %v", err)
	}

	// Format message for comparison
	tempClient := telegram.NewClient("", 0) // Temporary client just for formatting
	formattedMessage := tempClient.FormatMessage(message)

	if formattedMessage == lastMessage {
		log.Println("No new messages to send - offer unchanged")
		return nil
	}

	// Send new message
	if err := a.telegramClient.SendMessage(message); err != nil {
		return fmt.Errorf("failed to send telegram message: %w", err)
	}

	// Save the message for future duplicate checking
	if err := a.storage.SaveLastMessage(formattedMessage); err != nil {
		log.Printf("Warning: failed to save last message: %v", err)
	}

	log.Println("Message sent successfully!")
	return nil
}