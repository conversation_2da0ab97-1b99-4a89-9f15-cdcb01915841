package config

import (
	"encoding/json"
	"fmt"
	"os"
)

type Config struct {
	TelegramAPIKey    string   `json:"telegram_api_key"`
	TelegramChannelID int64    `json:"telegram_channel_id"`
	Websites          []string `json:"websites"`
	LastMessageFile   string   `json:"last_message_file"`
}

func Load() (*Config, error) {
	return LoadFromFile("config.json")
}

func LoadFromFile(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", filename, err)
	}

	var cfg Config
	if err := json.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file %s: %w", filename, err)
	}

	if err := cfg.validate(); err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid configuration: %w", err)
	}

	return &cfg, nil
}

func (c *Config) validate() error {
	if c.TelegramAPIKey == "" {
		return fmt.Errorf("telegram_api_key is required")
	}
	if c.TelegramChannelID == 0 {
		return fmt.Errorf("telegram_channel_id is required")
	}
	if len(c.Websites) == 0 {
		return fmt.Errorf("websites are required")
	}
	if c.LastMessageFile == "" {
		return fmt.Errorf("last_message_file is required")
	}
	return nil
}
