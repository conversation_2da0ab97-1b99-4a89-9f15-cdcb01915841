package config

import (
	"os"
	"testing"
)

func TestLoadFromFile(t *testing.T) {
	// Create a temporary config file
	tempFile := "test_config.json"
	defer os.Remove(tempFile)

	testConfig := `{
		"telegram_api_key": "test_key",
		"telegram_channel_id": -123456789,
		"website": "https://example.com",
		"last_message_file": "test_last_message.txt"
	}`

	if err := os.WriteFile(tempFile, []byte(testConfig), 0644); err != nil {
		t.Fatalf("Failed to create test config file: %v", err)
	}

	cfg, err := LoadFromFile(tempFile)
	if err != nil {
		t.Fatalf("LoadFromFile failed: %v", err)
	}

	if cfg.TelegramAPIKey != "test_key" {
		t.Errorf("Expected TelegramAPIKey to be 'test_key', got '%s'", cfg.TelegramAP<PERSON>Key)
	}

	if cfg.TelegramChannelID != -123456789 {
		t.<PERSON><PERSON><PERSON>("Expected TelegramChannelID to be -123456789, got %d", cfg.TelegramChannelID)
	}

	if cfg.Website != "https://example.com" {
		t.Errorf("Expected Website to be 'https://example.com', got '%s'", cfg.Website)
	}

	if cfg.LastMessageFile != "test_last_message.txt" {
		t.Errorf("Expected LastMessageFile to be 'test_last_message.txt', got '%s'", cfg.LastMessageFile)
	}
}

func TestLoadFromFileInvalidJSON(t *testing.T) {
	tempFile := "test_invalid_config.json"
	defer os.Remove(tempFile)

	invalidJSON := `{
		"telegram_api_key": "test_key"
		"missing_comma": true
	}`

	if err := os.WriteFile(tempFile, []byte(invalidJSON), 0644); err != nil {
		t.Fatalf("Failed to create test config file: %v", err)
	}

	_, err := LoadFromFile(tempFile)
	if err == nil {
		t.Error("Expected LoadFromFile to fail with invalid JSON")
	}
}

func TestValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      Config
		expectError bool
	}{
		{
			name: "valid config",
			config: Config{
				TelegramAPIKey:    "valid_key",
				TelegramChannelID: -123456789,
				Website:           "https://example.com",
				LastMessageFile:   "test.txt",
			},
			expectError: false,
		},
		{
			name: "missing api key",
			config: Config{
				TelegramChannelID: -123456789,
				Website:           "https://example.com",
				LastMessageFile:   "test.txt",
			},
			expectError: true,
		},
		{
			name: "missing channel id",
			config: Config{
				TelegramAPIKey:  "valid_key",
				Website:         "https://example.com",
				LastMessageFile: "test.txt",
			},
			expectError: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := test.config.validate()
			if test.expectError && err == nil {
				t.Error("Expected validation to fail")
			}
			if !test.expectError && err != nil {
				t.Errorf("Expected validation to pass, got error: %v", err)
			}
		})
	}
}