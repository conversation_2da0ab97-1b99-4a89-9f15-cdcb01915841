# Wizink Current Promo Scraper

A Go application that scrapes Wizink promotional offers and sends notifications via Telegram.

## Architecture

The application follows a clean architecture pattern with separation of concerns:

### Package Structure

- **`internal/app`** - Application orchestration and business logic
- **`internal/config`** - Configuration management with environment variable support
- **`internal/scraper`** - Web scraping functionality using Colly
- **`internal/telegram`** - Telegram Bot API client
- **`internal/storage`** - File-based storage for message deduplication

### Key Improvements

1. **Modular Design** - Each concern is separated into its own package
2. **Error Handling** - Proper error propagation and logging
3. **Testability** - Unit tests for core functionality
4. **Configuration** - JSON file-based configuration with validation
5. **No Duplicate Processing** - Fixed the issue where `final()` was called twice

## Configuration

The application reads configuration from a `config.json` file in the current directory. Create this file with the following structure:

```json
{
  "telegram_api_key": "your_telegram_bot_api_key",
  "telegram_channel_id": -1234567890,
  "website": "https://www.wizink.pt/mail/landing/aderir-cartao-de-credito-flash.html",
  "last_message_file": "last_message.txt"
}
```

### Configuration Fields

- `telegram_api_key` - Telegram Bot API key (required)
- `telegram_channel_id` - Target Telegram channel ID (required, negative number)
- `website` - URL to scrape for offers (required)
- `last_message_file` - File to store last sent message for deduplication (required)

## Usage

```bash
# Build the application
go build -o wizink-promo main.go

# Create config.json file (see Configuration section above)
# Then run the application
./wizink-promo
```

## Testing

```bash
# Run all tests
go test ./internal/...

# Run tests with coverage
go test -cover ./internal/...
```

## Development

The application uses Go modules for dependency management. Main dependencies:

- `github.com/gocolly/colly` - Web scraping framework

### Adding New Features

1. Follow the existing package structure
2. Add appropriate tests for new functionality
3. Update configuration if needed
4. Ensure proper error handling and logging